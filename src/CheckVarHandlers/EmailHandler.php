<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class EmailHandler {
    /**
     * @param int|string $options1
     * @param int|string $options2
     * @return void
     */
    public function process(int|string &$options1, int|string &$options2): void {
        $valid_options1 = [0, 1];
        $valid_options2 = [0, 1];
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 0;
        } else {
            $options1 = 1;
        }
        if ($options2 === '' || !in_array($options2, $valid_options2, true)) {
            $options2 = 0;
        } else {
            $options2 = 1;
        }
    }
}
