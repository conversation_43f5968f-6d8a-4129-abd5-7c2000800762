<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class SpecialHandler implements HandlerInterface {
    /**
     * @param mixed $options1
     * @param mixed $options2
     * @return void
     */
    public function process(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['striplow', 'striphigh', 'encodehigh'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
    }
}
