<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class UrlHandler {
    /**
     * @param string $options1
     * @param int|string $options2
     * @return array{string, int}
     */
    public function process(string $options1, int|string $options2): array {
        $valid_options1 = ['scheme', 'path', 'host', 'query'];
        $valid_options2 = [0, 1];
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
        if ($options2 === '' || !in_array($options2, $valid_options2, true)) {
            $options2 = 0;
        } else {
            $options2 = 1;
        }
        return [$options1, $options2];
    }
}
