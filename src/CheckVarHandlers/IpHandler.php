<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IpHandler {
    /**
     * @param string $options1
     * @param string $options2
     * @return void
     */
    public function process(string &$options1, string &$options2 = ''): void {
        $valid_options1 = ['ipv4', 'ipv6', 'rfc', 'res'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'ipv4';
        }
    }
}
