<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IpHandler implements HandlerInterface {
    /**
     * @param mixed $options1
     * @param mixed $options2
     * @return void
     */
    public function process(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['ipv4', 'ipv6', 'rfc', 'res'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'ipv4';
        }
    }
}
