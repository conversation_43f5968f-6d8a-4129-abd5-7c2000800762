<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IpHandler {
    /**
     * @param string $options1
     * @param string $options2
     * @return array{string, string}
     */
    public function process(string $options1, string $options2 = ''): array {
        $valid_options1 = ['ipv4', 'ipv6', 'rfc', 'res'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'ipv4';
        }
        return [$options1, $options2];
    }
}
