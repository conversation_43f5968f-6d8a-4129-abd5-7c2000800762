<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class HtmlHandler implements HandlerInterface {
    /**
     * @param mixed $options1
     * @param mixed $options2
     * @return void
     */
    public function process(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['input', 'output', 'print', 'edit'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'input';
        }
    }
}
