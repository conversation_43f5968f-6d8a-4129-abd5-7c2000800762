<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class HtmlHandler {
    /**
     * @param string $options1
     * @param string $options2
     * @return void
     */
    public function process(string &$options1, string &$options2 = ''): void {
        $valid_options1 = ['input', 'output', 'print', 'edit'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'input';
        }
    }
}
