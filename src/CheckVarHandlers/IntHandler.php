<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IntHandler {
    /**
     * @param int|string $options1
     * @param int|string $options2
     * @return array{int|string, int|string}
     */
    public function process(int|string $options1, int|string $options2): array {
        if (!is_int($options1) || !is_int($options2)) {
            $options1 = '';
            $options2 = '';
        } else {
            $options1 = (int) $options1;
            $options2 = (int) $options2;
        }
        return [$options1, $options2];
    }
}
