<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IntHandler implements HandlerInterface {
    /**
     * @param mixed $options1
     * @param mixed $options2
     * @return void
     */
    public function process(mixed &$options1, mixed &$options2): void {
        if (!is_int($options1) || !is_int($options2)) {
            $options1 = '';
            $options2 = '';
        } else {
            $options1 = (int) $options1;
            $options2 = (int) $options2;
        }
    }
}
