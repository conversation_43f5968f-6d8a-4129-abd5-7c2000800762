<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class IntHandler {
    /**
     * @param int|string $options1
     * @param int|string $options2
     * @return void
     */
    public function process(int|string &$options1, int|string &$options2): void {
        if (!is_int($options1) || !is_int($options2)) {
            $options1 = '';
            $options2 = '';
        } else {
            $options1 = (int) $options1;
            $options2 = (int) $options2;
        }
    }
}
