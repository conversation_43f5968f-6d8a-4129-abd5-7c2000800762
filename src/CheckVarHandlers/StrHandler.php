<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class StrHandler {
    /**
     * @param string $options1
     * @param string $options2
     * @return array{string, string}
     */
    public function process(string $options1, string $options2 = ''): array {
        $valid_options1 = ['noencode', 'striplow', 'striphigh', 'encodelow', 'encodehigh', 'encodeamp'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
        return [$options1, $options2];
    }
}
