<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

class TextHandler {
    /**
     * @param string $options1
     * @param string $options2
     * @return array{string, string}
     */
    public function process(string $options1, string $options2 = ''): array {
        $valid_options1 = ['input', 'output', 'print'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'input';
        }
        return [$options1, $options2];
    }
}
